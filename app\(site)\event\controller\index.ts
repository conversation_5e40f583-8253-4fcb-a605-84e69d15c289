import { client } from "@/sanity/lib/client";
import {
  getAllEventsQuery,
  getEventBySlugQuery,
  getEventLocationsQuery,
  getEventOrganizersQuery,
  getEventByIdQuery,
} from "@/sanity/queries/event";
import { z } from "zod";
import { eventSchema, EventFiltersType, eventFiltersSchema } from "../schema";
import { LocaleType, DEFAULT_LANGUAGE } from "../../api/i18n";

export const getAllEvents = async (
  filters?: EventFiltersType,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  let validatedFilters: EventFiltersType = {};

  if (filters) {
    const parsedFilters = eventFiltersSchema.safeParse(filters);
    if (parsedFilters.success) {
      validatedFilters = parsedFilters.data;
    } else {
      console.error("Invalid filters:", parsedFilters.error);
      return null;
    }
  }
  const events = await client
    .fetch(getAllEventsQuery(validatedFilters), { lang })
    .catch((err) => {
      console.error(err);
      return null;
    });

  if (!events) {
    return null;
  }

  console.dir({
    locations: events.map((event: any) => ({
      name: event.name,
      province: event.locationObj.province,
    })),
  });

  const schema = z.array(eventSchema).safeParse(events);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const getEventBySlug = async (
  slug: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const event = await client
    .fetch(getEventBySlugQuery, { slug, lang })
    .catch((err) => {
      console.error(err);
      return null;
    });

  const schema = eventSchema.safeParse(event);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

export const getEventById = async (
  id: string,
  lang: LocaleType = DEFAULT_LANGUAGE,
) => {
  const event = await client
    .fetch(getEventByIdQuery, { id, lang })
    .catch((err) => {
      console.error(err);
      return null;
    });

  const schema = eventSchema.safeParse(event);

  if (!schema.success) {
    console.error(schema.error);
    return null;
  }

  return schema.data;
};

type LocationType = {
  name: string;
};

export const getEventsLocations = async (): Promise<LocationType[] | null> => {
  const locations = await client.fetch(getEventLocationsQuery).catch((err) => {
    console.error("Error fetching event locations:", err);
    return null;
  });

  if (!locations) {
    return null;
  }

  return z.array(z.object({ name: z.string() })).parse(locations);
};

type OrganizerType = {
  name: string;
};

export const getEventsOrganizers = async (): Promise<
  OrganizerType[] | null
> => {
  const organizers = await client
    .fetch(getEventOrganizersQuery)
    .catch((err) => {
      console.error("Error fetching event organizers:", err);
      return null;
    });

  if (!organizers) {
    return null;
  }

  return z.array(z.object({ name: z.string() })).parse(organizers);
};
